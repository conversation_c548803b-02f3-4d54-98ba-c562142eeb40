import { changeSignalsDowntimesFactory, changeSignalsIssuesFactory } from '@shape-construction/api/factories/control-center';
import { disciplineFactory } from '@shape-construction/api/factories/disciplines';
import { issueFactory } from '@shape-construction/api/factories/issues';
import { locationFactory } from '@shape-construction/api/factories/locations';
import { projectFactory } from '@shape-construction/api/factories/projects';
import { shiftReportsDownTimeFactory, shiftReportsFactory } from '@shape-construction/api/factories/shiftReports';
import { teamMemberFactory } from '@shape-construction/api/factories/team-member';
import { userBasicDetailsFactory, userFactory } from '@shape-construction/api/factories/users';
import { getApiProjectsProjectIdMockHandler } from '@shape-construction/api/handlers-factories/projects';
import { postApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksBatchDeleteMockHandler } from '@shape-construction/api/handlers-factories/projects/control-center';
import { getApiProjectsProjectIdDisciplinesMockHandler } from '@shape-construction/api/handlers-factories/projects/disciplines';
import { getApiProjectsProjectIdLocationsMockHandler } from '@shape-construction/api/handlers-factories/projects/locations';
import { getApiProjectsProjectIdShiftReportsShiftReportIdMockHandler } from '@shape-construction/api/handlers-factories/projects/shift-reports';
import { getApiProjectsProjectIdPeopleMockHandler } from '@shape-construction/api/handlers-factories/projects/team-members';
import { server } from 'tests/mock-server';
import { render, screen } from 'tests/test-utils';
import { PotentialChangeSignalItem } from './PotentialChangeSignalItem';

describe('<PotentialChangeSignalItem />', () => {
  it.skip('renders the signal information correctly', async () => {
    const project = projectFactory({ id: 'project-1', timezone: 'Europe/London' });
    const location = locationFactory({ id: 'location-1', name: 'Test Location' });
    const user = userBasicDetailsFactory({ id: 'user-1', name: 'Test user' });
    const teamMember = teamMemberFactory({ id: 1, user });
    const signal = changeSignalsIssuesFactory({
      signalId: 'issue-1',
      signalType: 'issue',
      title: 'Test Issue',
      publishedAt: '2023-05-15T10:30:00Z',
      locationId: location.id,
      teamMemberId: teamMember.id,
    });
    server.use(
      getApiProjectsProjectIdMockHandler(() => project),
      getApiProjectsProjectIdLocationsMockHandler(() => [location]),
      getApiProjectsProjectIdPeopleMockHandler(() => [teamMember])
    );

    render(<PotentialChangeSignalItem onUnlinkChangeSignalClick={() => {}} signal={signal} canUnlink />);

    expect(await screen.findByText('Test Issue')).toBeInTheDocument();
    expect(screen.getByText('issue')).toBeInTheDocument();
    expect(screen.getByText('15-May-23')).toBeInTheDocument();
    expect(await screen.findByText('Test Location')).toBeInTheDocument();
    expect(screen.getByText('TE')).toBeInTheDocument();
    expect(await screen.findByText('Test user')).toBeInTheDocument();
  });

  describe('when canUnlink is true', () => {
    it('renders unlink button', async () => {
      const project = projectFactory({ id: 'project-1', timezone: 'Europe/London' });
      const location = locationFactory({ id: 'location-1', name: 'Test Location' });
      const user = userFactory({ id: 'user-1', name: 'Test user' });
      const teamMember = teamMemberFactory({ id: 1, user });
      const signal = changeSignalsIssuesFactory();
      server.use(
        getApiProjectsProjectIdMockHandler(() => project),
        getApiProjectsProjectIdLocationsMockHandler(() => [location]),
        getApiProjectsProjectIdPeopleMockHandler(() => [teamMember])
      );

      render(<PotentialChangeSignalItem onUnlinkChangeSignalClick={() => {}} signal={signal} canUnlink />);

      expect(
        await screen.findByRole('button', {
          name: 'controlCenter.commercialTracker.modals.potentialChangeLinkSignalsDrawer.unlink',
        })
      ).toBeInTheDocument();
    });
  });

  describe('when canUnlink is false', () => {
    it('does not render unlink button', async () => {
      const project = projectFactory({ id: 'project-1', timezone: 'Europe/London' });
      const location = locationFactory({ id: 'location-1', name: 'Test Location' });
      const user = userFactory({ id: 'user-1', name: 'Test user' });
      const teamMember = teamMemberFactory({ id: 1, user });
      const signal = changeSignalsIssuesFactory();
      server.use(
        getApiProjectsProjectIdMockHandler(() => project),
        getApiProjectsProjectIdLocationsMockHandler(() => [location]),
        getApiProjectsProjectIdPeopleMockHandler(() => [teamMember])
      );

      render(<PotentialChangeSignalItem onUnlinkChangeSignalClick={() => {}} signal={signal} canUnlink={false} />);

      const unlinkButton = screen.queryByRole('button', {
        name: 'controlCenter.commercialTracker.modals.potentialChangeLinkSignalsDrawer.unlink',
      });
      expect(unlinkButton).not.toBeInTheDocument();
    });
  });

  describe('when the isUnlinkingChangeSignals prop is true', () => {
    it('disables the unlink button', async () => {
      const project = projectFactory({ id: 'project-1', timezone: 'Europe/London' });
      const location = locationFactory({ id: 'location-1', name: 'Test Location' });
      const user = userBasicDetailsFactory({ id: 'user-1', name: 'Test user' });
      const teamMember = teamMemberFactory({ id: 1, user });
      const signal = changeSignalsIssuesFactory();
      server.use(
        getApiProjectsProjectIdMockHandler(() => project),
        getApiProjectsProjectIdLocationsMockHandler(() => [location]),
        getApiProjectsProjectIdPeopleMockHandler(() => [teamMember]),
        postApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksBatchDeleteMockHandler()
      );

      render(
        <PotentialChangeSignalItem
          onUnlinkChangeSignalClick={() => {}}
          signal={signal}
          isUnlinkingChangeSignals={true}
          canUnlink
        />
      );

      expect(
        await screen.findByRole('button', {
          name: 'controlCenter.commercialTracker.modals.potentialChangeLinkSignalsDrawer.unlink',
        })
      ).toBeDisabled();
    });
  });

  describe('when signal type is issue', () => {
    it('renders all issue-specific details correctly', async () => {
      const project = projectFactory({ id: 'project-1', timezone: 'Europe/London' });
      const location = locationFactory({ id: 'location-1', name: 'Test Location' });
      const user = userBasicDetailsFactory({ id: 'user-1', name: 'Test User' });
      const teamMember = teamMemberFactory({ id: 1, user });
      const discipline = disciplineFactory({ id: 'discipline-1', name: 'Electrical' });

      const issue = issueFactory({
        id: 'issue-id-1',
        title: 'Issue Title',
        description: 'Detailed issue description',
        disciplineId: discipline.id,
        currentState: 'in_progress',
        observedAt: '2023-05-14T08:00:00Z',
        dueDate: '2023-05-20T17:00:00Z',
        plannedClosureDate: '2023-05-18T15:00:00Z',
        closedAt: null,
        delayStart: '2023-05-16T09:00:00Z',
        delayFinish: '2023-05-17T16:00:00Z',
      });

      const signal = changeSignalsIssuesFactory({
        signalId: 'issue-1',
        signalType: 'issue',
        title: 'Critical Issue',
        publishedAt: '2023-05-15T10:30:00Z',
        locationId: location.id,
        teamMemberId: teamMember.id,
        impact: 'liveDelay',
        issue,
      });

      server.use(
        getApiProjectsProjectIdMockHandler(() => project),
        getApiProjectsProjectIdLocationsMockHandler(() => [location]),
        getApiProjectsProjectIdPeopleMockHandler(() => [teamMember]),
        getApiProjectsProjectIdDisciplinesMockHandler(() => [discipline])
      );

      render(<PotentialChangeSignalItem onUnlinkChangeSignalClick={() => { }} signal={signal} canUnlink />);

      // Verify signal type and title
      expect(await screen.findByText('Critical Issue', { selector: 'span' })).toBeInTheDocument();
      expect(screen.getByText('issue')).toBeInTheDocument();

      // Verify author information
      expect(screen.getByText('TE')).toBeInTheDocument(); // Avatar initials
      expect(await screen.findByText('Test User')).toBeInTheDocument();

      // Verify created date
      expect(screen.getByText('15-May-23')).toBeInTheDocument();

      // Verify impact
      expect(screen.getByText('liveDelay')).toBeInTheDocument();

      // Verify status
      expect(screen.getByText('in_progress')).toBeInTheDocument();

      // Verify description
      expect(screen.getByText('Detailed issue description')).toBeInTheDocument();

      // Verify discipline
      expect(screen.getByText('Electrical')).toBeInTheDocument();

      // Verify location
      expect(await screen.findByText('Test Location')).toBeInTheDocument();

      // Verify observed date
      expect(screen.getByText('14-May-2023 09:00')).toBeInTheDocument();

      // Verify due date
      expect(screen.getByText('20-May-2023 18:00')).toBeInTheDocument();

      // Verify planned closure date
      expect(screen.getByText('18-May-2023 16:00')).toBeInTheDocument();

      // Verify delay start
      expect(screen.getByText('16-May-2023 10:00')).toBeInTheDocument();

      // Verify delay finish
      expect(screen.getByText('17-May-2023 17:00')).toBeInTheDocument();

      // Verify closed at shows '--' when null
      expect(screen.getByText('--')).toBeInTheDocument();
    });
  });

  describe('when signal type is downtime', () => {
    it.skip('renders all downtime-specific details correctly', async () => {
      const project = projectFactory({ id: 'project-1', timezone: 'Europe/London' });
      const location = locationFactory({ id: 'location-1', name: 'Test Location' });
      const user = userBasicDetailsFactory({ id: 'user-1', name: 'Test User' });
      const teamMember = teamMemberFactory({ id: 1, user });

      const shiftReport = shiftReportsFactory({
        id: 'shift-report-1',
        shiftType: 'Day Shift',
      });

      const downtime = shiftReportsDownTimeFactory({
        id: 'downtime-1',
        causalType: 'Equipment Failure',
        timeLost: 4.5,
        issueId: 'linked-issue-1',
        issueDescription: 'Pump malfunction causing delays',
      });

      const signal = changeSignalsDowntimesFactory({
        signalId: 'downtime-1',
        signalType: 'downtime',
        title: 'Equipment Downtime',
        publishedAt: '2023-05-15T14:30:00Z',
        locationId: location.id,
        teamMemberId: teamMember.id,
        shiftReportId: shiftReport.id,
        downtime,
        shiftReport,
      });

      server.use(
        getApiProjectsProjectIdMockHandler(() => project),
        getApiProjectsProjectIdLocationsMockHandler(() => [location]),
        getApiProjectsProjectIdPeopleMockHandler(() => [teamMember]),
        getApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(() => shiftReport)
      );

      render(<PotentialChangeSignalItem onUnlinkChangeSignalClick={() => { }} signal={signal} canUnlink />);

      // Verify signal type and title
      expect(await screen.findByText('Equipment Downtime')).toBeInTheDocument();
      expect(screen.getByText('downtime')).toBeInTheDocument();

      // Verify author information
      expect(screen.getByText('TE')).toBeInTheDocument(); // Avatar initials
      expect(await screen.findByText('Test User')).toBeInTheDocument();

      // Verify created date
      expect(screen.getByText('15-May-23')).toBeInTheDocument();

      // Verify shift type
      expect(screen.getByText('Day Shift')).toBeInTheDocument();

      // Verify time lost
      expect(screen.getByText('4.5')).toBeInTheDocument();

      // Verify causal type/reason
      expect(screen.getByText('Equipment Failure')).toBeInTheDocument();

      // Verify linked issue description
      expect(screen.getByText('Pump malfunction causing delays')).toBeInTheDocument();

      // Verify description (uses downtime.issueDescription)
      expect(screen.getByText('Pump malfunction causing delays')).toBeInTheDocument();

      // Verify location
      expect(await screen.findByText('Test Location')).toBeInTheDocument();
    });

    it.skip('renders downtime without linked issue correctly', async () => {
      const project = projectFactory({ id: 'project-1', timezone: 'Europe/London' });
      const location = locationFactory({ id: 'location-1', name: 'Test Location' });
      const user = userBasicDetailsFactory({ id: 'user-1', name: 'Test User' });
      const teamMember = teamMemberFactory({ id: 1, user });

      const shiftReport = shiftReportsFactory({
        id: 'shift-report-1',
        shiftType: 'Night Shift',
      });

      const downtime = shiftReportsDownTimeFactory({
        id: 'downtime-2',
        causalType: 'Weather Delay',
        timeLost: 2.0,
        issueId: null,
        issueDescription: 'Heavy rain prevented work',
      });

      const signal = changeSignalsDowntimesFactory({
        signalId: 'downtime-2',
        signalType: 'downtime',
        title: 'Weather Downtime',
        publishedAt: '2023-05-16T08:00:00Z',
        locationId: location.id,
        teamMemberId: teamMember.id,
        shiftReportId: shiftReport.id,
        downtime,
        shiftReport,
      });

      server.use(
        getApiProjectsProjectIdMockHandler(() => project),
        getApiProjectsProjectIdLocationsMockHandler(() => [location]),
        getApiProjectsProjectIdPeopleMockHandler(() => [teamMember]),
        getApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(() => shiftReport)
      );

      render(<PotentialChangeSignalItem onUnlinkChangeSignalClick={() => { }} signal={signal} canUnlink />);

      // Verify signal type and title
      expect(await screen.findByText('Weather Downtime')).toBeInTheDocument();
      expect(screen.getByText('downtime')).toBeInTheDocument();

      // Verify shift type
      expect(screen.getByText('Night Shift')).toBeInTheDocument();

      // Verify time lost
      expect(screen.getByText('2')).toBeInTheDocument();

      // Verify causal type/reason
      expect(screen.getByText('Weather Delay')).toBeInTheDocument();

      // Verify no linked issue shows '--'
      expect(screen.getByText('--')).toBeInTheDocument();

      // Verify description
      expect(screen.getByText('Heavy rain prevented work')).toBeInTheDocument();
    });
  });
});
