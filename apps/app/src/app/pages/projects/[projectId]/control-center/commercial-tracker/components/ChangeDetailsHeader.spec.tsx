import { potentialChangeFactory } from '@shape-construction/api/factories/control-center';
import { teamMemberFactory } from '@shape-construction/api/factories/team-member';
import { userBasicDetailsFactory } from '@shape-construction/api/factories/users';
import { getApiProjectsProjectIdMockHandler } from '@shape-construction/api/handlers-factories/projects';
import { getApiProjectsProjectIdPeopleMockHandler } from '@shape-construction/api/handlers-factories/projects/team-members';
import { server } from 'tests/mock-server';
import { render, screen } from 'tests/test-utils';
import { ChangeDetailsHeader } from './ChangeDetailsHeader';

describe('<ChangeDetailsHeader />', () => {
  it('renders the component with correct data', async () => {
    const teamMember = teamMemberFactory({ id: 1, user: userBasicDetailsFactory({ id: '123', name: '<PERSON>' }) });
    const change = potentialChangeFactory({
      title: 'title test',
      teamMemberId: teamMember.id,
      createdAt: '2023-01-15T10:30:00Z',
    });
    server.use(
      getApiProjectsProjectIdMockHandler(),
      getApiProjectsProjectIdPeopleMockHandler(() => [teamMember])
    );

    render(<ChangeDetailsHeader change={change} />);

    expect(await screen.findByText('title test')).toBeInTheDocument();
    expect(await screen.findByText('John Doe')).toBeInTheDocument();
    expect(await screen.findByText('15-Jan-2023')).toBeInTheDocument();
  });
});
